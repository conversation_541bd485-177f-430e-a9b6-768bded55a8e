# GitHub Actions CI/CD Setup Guide for Filtro Curricular Azure

This guide provides step-by-step instructions to set up automated CI/CD deployment using GitHub Actions for the Filtro Curricular Azure infrastructure.

## 📋 Overview

The CI/CD setup includes:

- **Infrastructure Workflow**: Deploys Terraform infrastructure when changes are detected in infrastructure files
- **Application Workflow**: Builds and deploys applications when code changes are detected
- **Environment Support**: dev/staging/uat/prod with appropriate controls
- **Security**: Service Principal authentication with minimal permissions
- **Notifications**: Email and Microsoft Teams integration
- **Branch Protection**: Deployment restrictions based on branch rules

## 🏗️ Architecture

### Naming Convention

```terraform
Resource Pattern: {project}-{resource-type}-{environment}-{region-code}
Examples:
- filtro-curricular-rg-dev-eus          (Resource Group)
- filtro-curricular-acr-dev-eus         (Container Registry)
- filtro-curricular-app-be-dev-eus      (Backend App Service)
- filtro-curricular-app-fe-dev-eus      (Frontend App Service)
- filtro-curricular-kv-dev-eus-abc123   (Key Vault with random suffix)
```

### Environment Strategy

- **dev**: Auto-deploy from `dev` branch, auto-shutdown after 5 min idle, single instance
- **staging**: Auto-deploy from `main` branch, manual approval for infrastructure changes
- **uat**: Manual deployment only, manual approval required
- **prod**: Manual deployment only, manual approval required

## 🔐 Step 1: Create Azure Service Principal

### 1.1 Create Service Principal with Minimal Permissions

```bash
# Set variables
SUBSCRIPTION_ID="your-subscription-id"
SP_NAME="sp-filtro-curricular-github-actions"
RESOURCE_GROUP_PREFIX="filtro-curricular-rg"

# Login to Azure
az login
az account set --subscription "$SUBSCRIPTION_ID"

# Create Service Principal
SP_OUTPUT=$(az ad sp create-for-rbac \
  --name "$SP_NAME" \
  --role "Contributor" \
  --scopes "/subscriptions/$SUBSCRIPTION_ID" \
  --sdk-auth)

echo "Service Principal created. Save this output securely:"
echo "$SP_OUTPUT"
```

### 1.2 Assign Additional Required Permissions

```bash
# Get Service Principal Object ID
SP_OBJECT_ID=$(az ad sp show --id "http://$SP_NAME" --query objectId -o tsv)

# Assign Key Vault Administrator role (for managing secrets)
az role assignment create \
  --assignee "$SP_OBJECT_ID" \
  --role "Key Vault Administrator" \
  --scope "/subscriptions/$SUBSCRIPTION_ID"

# Assign User Access Administrator role (for role assignments)
az role assignment create \
  --assignee "$SP_OBJECT_ID" \
  --role "User Access Administrator" \
  --scope "/subscriptions/$SUBSCRIPTION_ID"

echo "Additional permissions assigned to Service Principal"
```

### 1.3 Create Resource Groups for Each Environment

```bash
# Create resource groups for all environments
ENVIRONMENTS=("dev" "staging" "uat" "prod")
LOCATION="Chile Central"

for ENV in "${ENVIRONMENTS[@]}"; do
  RG_NAME="filtro-curricular-rg-$ENV-eus"
  az group create --name "$RG_NAME" --location "$LOCATION"
  echo "Created resource group: $RG_NAME"
done
```

## 🔑 Step 2: Configure GitHub Repository Secrets

### 2.1 Required GitHub Secrets

Navigate to your GitHub repository → Settings → Secrets and variables → Actions

**Repository Secrets:**

```
AZURE_CREDENTIALS
# Value: The complete JSON output from Service Principal creation
# Example:
{
  "clientId": "12345678-1234-1234-1234-123456789012",
  "clientSecret": "your-client-secret",
  "subscriptionId": "your-subscription-id",
  "tenantId": "your-tenant-id"
}

OPENAI_API_KEY
# Value: sk-your-openai-api-key-here

OPENAI_TOKEN  
# Value: your-openai-token-here

ASSISTANT_ID_JURIDICO
# Value: asst_your-juridico-assistant-id

ASSISTANT_ID_CALIDAD
# Value: asst_your-calidad-assistant-id

NOTIFICATION_EMAIL
# Value: <EMAIL>

TEAMS_WEBHOOK_URL
# Value: https://your-company.webhook.office.com/webhookb2/...
```

### 2.2 Environment-Specific Secrets

For each environment (dev, staging, uat, prod), create environment-specific secrets:

**Environment: dev**

```
AZURE_SUBSCRIPTION_ID_DEV
# Value: your-dev-subscription-id (can be same as main)

OPENAI_API_KEY_DEV
# Value: sk-your-dev-openai-api-key (can be same or different)
```

**Environment: staging**

```
AZURE_SUBSCRIPTION_ID_STAGING
# Value: your-staging-subscription-id

OPENAI_API_KEY_STAGING
# Value: sk-your-staging-openai-api-key
```

**Environment: uat**

```
AZURE_SUBSCRIPTION_ID_UAT
# Value: your-uat-subscription-id

OPENAI_API_KEY_UAT
# Value: sk-your-uat-openai-api-key
```

**Environment: prod**

```
AZURE_SUBSCRIPTION_ID_PROD
# Value: your-prod-subscription-id

OPENAI_API_KEY_PROD
# Value: sk-your-prod-openai-api-key
```

## 📊 Step 3: Configure GitHub Repository Variables

Navigate to your GitHub repository → Settings → Secrets and variables → Actions → Variables

**Repository Variables:**

```
AZURE_LOCATION
# Value: Chile Central

AZURE_LOCATION_CODE
# Value: eus

PROJECT_NAME
# Value: filtro-curricular

TERRAFORM_VERSION
# Value: 1.5.0
```

**Environment-Specific Variables:**

**Environment: dev**

```
APP_SERVICE_PLAN_SKU_DEV
# Value: B1

ACR_SKU_DEV
# Value: Basic

AUTO_SHUTDOWN_ENABLED_DEV
# Value: true

MIN_INSTANCES_DEV
# Value: 0

MAX_INSTANCES_DEV
# Value: 1
```

**Environment: staging**

```
APP_SERVICE_PLAN_SKU_STAGING
# Value: S1

ACR_SKU_STAGING
# Value: Standard

MIN_INSTANCES_STAGING
# Value: 1

MAX_INSTANCES_STAGING
# Value: 2
```

**Environment: uat**

```
APP_SERVICE_PLAN_SKU_UAT
# Value: S1

ACR_SKU_UAT
# Value: Standard

MIN_INSTANCES_UAT
# Value: 1

MAX_INSTANCES_UAT
# Value: 2
```

**Environment: prod**

```
APP_SERVICE_PLAN_SKU_PROD
# Value: P1v2

ACR_SKU_PROD
# Value: Premium

MIN_INSTANCES_PROD
# Value: 2

MAX_INSTANCES_PROD
# Value: 10
```

## 🔄 Step 4: Create GitHub Environments

### 4.1 Create Environments with Protection Rules

Navigate to your GitHub repository → Settings → Environments

**Create the following environments:**

1. **dev**
   - No protection rules
   - Auto-deploy from `dev` branch

2. **staging**
   - Protection rules: Required reviewers (1 person)
   - Auto-deploy from `main` branch

3. **uat**
   - Protection rules: Required reviewers (2 people)
   - Manual deployment only

4. **prod**
   - Protection rules: Required reviewers (3 people)
   - Manual deployment only
   - Deployment branch rule: Only `main` branch

### 4.2 Configure Environment Secrets and Variables

For each environment created above, add the environment-specific secrets and variables listed in Steps 2.2 and 3.

## 📧 Step 5: Configure Notifications

### 5.1 Email Notifications Setup

Add to repository secrets:

```bash
NOTIFICATION_EMAIL="<EMAIL>"
SMTP_SERVER="smtp.office365.com"  # For Office 365
SMTP_PORT="587"
SMTP_USERNAME="<EMAIL>"
SMTP_PASSWORD="your-smtp-password"
```

### 5.2 Microsoft Teams Webhook Setup

1. In Microsoft Teams, go to your channel
2. Click "..." → "Connectors" → "Incoming Webhook"
3. Configure webhook and copy the URL
4. Add to repository secrets:

```
TEAMS_WEBHOOK_URL="https://your-company.webhook.office.com/webhookb2/..."
```

## 🌿 Step 6: Configure Branch Protection Rules

### 6.1 Main Branch Protection

Navigate to your GitHub repository → Settings → Branches

**Create protection rule for `main` branch:**

```
Branch name pattern: main

Protection settings:
☑️ Require a pull request before merging
  ☑️ Require approvals (2)
  ☑️ Dismiss stale PR approvals when new commits are pushed
  ☑️ Require review from code owners

☑️ Require status checks to pass before merging
  ☑️ Require branches to be up to date before merging
  Required status checks:
    - terraform-plan
    - build-and-test

☑️ Require conversation resolution before merging
☑️ Require signed commits
☑️ Include administrators
☑️ Restrict pushes that create files larger than 100MB
```

### 6.2 Dev Branch Protection

**Create protection rule for `dev` branch:**

```
Branch name pattern: dev

Protection settings:
☑️ Require a pull request before merging
  ☑️ Require approvals (1)

☑️ Require status checks to pass before merging
  Required status checks:
    - terraform-plan
    - build-and-test

☑️ Include administrators
```

## 🚀 Step 7: Deploy Workflow Files

### 7.1 Copy Workflow Files to Correct Location

The workflow files have been created and need to be in the `.github/workflows/` directory:

```bash
# Infrastructure workflow (already created)
.github/workflows/filtro-curricular-infrastructure.yml

# Application workflow (already created)
.github/workflows/filtro-curricular-applications.yml
```

### 7.2 Workflow Triggers

**Infrastructure Workflow** (`filtro-curricular-infrastructure.yml`):

- Triggers on changes to `apps/infrastructure/azure/filtro-curricular/**`
- Deploys to `dev` from `dev` branch
- Deploys to `staging` from `main` branch
- Manual deployment to any environment via workflow_dispatch

**Application Workflow** (`filtro-curricular-applications.yml`):

- Triggers on changes to application directories
- Automatically detects which component changed (backend/frontend)
- Same branch-to-environment mapping as infrastructure

## ✅ Step 8: Verification and Testing

### 8.1 Test Service Principal Permissions

```bash
# Test Azure login with Service Principal
az login --service-principal \
  --username "your-client-id" \
  --password "your-client-secret" \
  --tenant "your-tenant-id"

# Test resource group access
az group list --query "[?contains(name, 'filtro-curricular')]"

# Test subscription access
az account show
```

### 8.2 Test GitHub Secrets

1. Go to your repository → Actions
2. Run the infrastructure workflow manually
3. Check that all secrets are properly configured
4. Verify no secret values are exposed in logs

### 8.3 Test Notifications

**Email Test:**

```bash
# Send test email using the same configuration
curl -X POST "https://api.emailjs.com/api/v1.0/email/send" \
  -H "Content-Type: application/json" \
  -d '{
    "service_id": "your_service",
    "template_id": "your_template",
    "user_id": "your_user_id",
    "template_params": {
      "to_email": "'$NOTIFICATION_EMAIL'",
      "subject": "Test Notification",
      "message": "GitHub Actions notification test"
    }
  }'
```

**Teams Test:**

```bash
# Send test message to Teams webhook
curl -X POST "$TEAMS_WEBHOOK_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Test Notification",
    "text": "GitHub Actions notification test"
  }'
```

## 🔄 Step 9: First Deployment

### 9.1 Deploy Infrastructure First

1. Create a new branch from `dev`:

   ```bash
   git checkout dev
   git pull origin dev
   git checkout -b feature/setup-azure-infrastructure
   ```

2. Make a small change to trigger the workflow:

   ```bash
   echo "# Infrastructure setup" >> apps/infrastructure/azure/filtro-curricular/README.md
   git add .
   git commit -m "feat: setup Azure infrastructure for filtro-curricular"
   git push origin feature/setup-azure-infrastructure
   ```

3. Create PR to `dev` branch and merge
4. Monitor the workflow execution in GitHub Actions

### 9.2 Deploy Applications

1. After infrastructure is deployed, make a change to an application:

   ```bash
   git checkout dev
   git pull origin dev
   git checkout -b feature/deploy-applications

   # Make a small change to trigger deployment
   echo "# Application deployment" >> apps/filtro-curricular/filtro-curricular-be-api/README.md
   git add .
   git commit -m "feat: deploy applications to Azure"
   git push origin feature/deploy-applications
   ```

2. Create PR to `dev` branch and merge
3. Monitor the application deployment workflow

## 🛠️ Step 10: Troubleshooting

### 10.1 Common Issues

**Service Principal Authentication Failed:**

```bash
# Verify Service Principal exists
az ad sp show --id "your-client-id"

# Check role assignments
az role assignment list --assignee "your-client-id"
```

**Terraform State Issues:**

```bash
# Initialize Terraform backend (if using remote state)
terraform init -reconfigure

# Import existing resources if needed
terraform import azurerm_resource_group.main /subscriptions/sub-id/resourceGroups/rg-name
```

**Container Registry Access Denied:**

```bash
# Check ACR permissions
az acr show --name your-acr-name --query "adminUserEnabled"

# Test ACR login
az acr login --name your-acr-name
```

### 10.2 Debugging Workflows

**Enable Debug Logging:**
Add these secrets to your repository:

```
ACTIONS_STEP_DEBUG = true
ACTIONS_RUNNER_DEBUG = true
```

**Check Workflow Logs:**

1. Go to GitHub repository → Actions
2. Click on the failed workflow run
3. Expand the failed step to see detailed logs
4. Check for error messages and stack traces

### 10.3 Rollback Procedures

**Rollback Application:**

```bash
# Manually trigger workflow with previous commit SHA
# Or use Azure CLI to update App Service to previous image
az webapp config container set \
  --name your-app-name \
  --resource-group your-rg \
  --docker-custom-image-name "your-acr.azurecr.io/app:previous-tag"
```

**Rollback Infrastructure:**

```bash
# Use Terraform to rollback to previous state
terraform plan -destroy  # Review what will be destroyed
terraform apply -auto-approve  # Apply previous configuration
```

## 📚 Step 11: Documentation Updates

The following documentation files have been updated to reflect GitHub Actions as the primary deployment method:

- ✅ `README.md` - Updated with GitHub Actions instructions
- ✅ `QUICK_START.md` - GitHub-focused quick start
- ✅ `MIGRATION_SUMMARY.md` - CI/CD setup status included
- ✅ `GITHUB_ACTIONS_SETUP.md` - This comprehensive guide

## 🎯 Summary

After completing this setup, you will have:

- ✅ Azure Service Principal with minimal required permissions
- ✅ GitHub repository configured with all necessary secrets and variables
- ✅ Separate workflows for infrastructure and application deployments
- ✅ Environment-specific deployments (dev/staging/uat/prod)
- ✅ Automated notifications via email and Microsoft Teams
- ✅ Branch protection rules enforcing proper review processes
- ✅ Comprehensive error handling and rollback mechanisms

The CI/CD pipeline will automatically:

- Deploy infrastructure changes when Terraform files are modified
- Build and deploy applications when code changes are detected
- Send notifications on success or failure
- Enforce environment-specific approval requirements
- Maintain proper security through managed identities and secrets
