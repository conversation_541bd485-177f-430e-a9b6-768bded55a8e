# 📢 Microsoft Teams Webhook Setup Guide

This guide walks you through setting up Microsoft Teams notifications for your Filtro Curricular CI/CD pipeline.

## 🎯 Overview

The GitHub Actions workflows will send notifications to Microsoft Teams for:
- ✅ Successful deployments
- ❌ Failed deployments
- 📊 Deployment summaries with resource URLs
- 🔄 Environment-specific notifications

## 📋 Prerequisites

- Microsoft Teams access
- Admin permissions for the Teams channel where you want notifications
- Access to configure connectors in Teams

## 🔧 Step 1: Create Incoming Webhook in Teams

### 1.1 Navigate to Your Teams Channel

1. **Open Microsoft Teams**:
   - Go to [teams.microsoft.com](https://teams.microsoft.com)
   - Navigate to the team and channel where you want notifications

2. **Access Channel Settings**:
   - Click the "..." (three dots) next to your channel name
   - Select "Connectors" from the dropdown menu

### 1.2 Configure Incoming Webhook

1. **Find Incoming Webhook**:
   - In the Connectors dialog, search for "Incoming Webhook"
   - Click "Configure" next to "Incoming Webhook"

2. **Configure Webhook**:
   - **Name**: `Filtro Curricular CI/CD`
   - **Upload Image**: Optional - you can upload a custom icon
   - Click "Create"

3. **Copy Webhook URL**:
   - **IMPORTANT**: Copy the webhook URL immediately
   - It will look like: `https://your-tenant.webhook.office.com/webhookb2/...`
   - Store this securely - you'll need it for GitHub secrets

4. **Complete Setup**:
   - Click "Done" to finish the webhook configuration

## 🔐 Step 2: Add Webhook to GitHub Secrets

### 2.1 Add Repository Secret

1. **Navigate to GitHub Repository**:
   - Go to your ragtech repository on GitHub
   - Click "Settings" → "Secrets and variables" → "Actions"

2. **Add Teams Webhook Secret**:
   - Click "New repository secret"
   - **Name**: `TEAMS_WEBHOOK_URL`
   - **Value**: Paste the webhook URL you copied from Teams
   - Click "Add secret"

## 📝 Step 3: Test Webhook Configuration

### 3.1 Test with curl (CLI Method)

#### **Basic Test**:
```bash
# Replace YOUR_WEBHOOK_URL with your actual webhook URL
WEBHOOK_URL="https://your-company.webhook.office.com/webhookb2/..."

curl -X POST "$WEBHOOK_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Test Notification",
    "text": "This is a test message from Filtro Curricular CI/CD setup",
    "themeColor": "0078D4"
  }'
```

#### **Advanced Test with Rich Formatting**:
```bash
# Test with deployment-like notification
curl -X POST "$WEBHOOK_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "@type": "MessageCard",
    "@context": "http://schema.org/extensions",
    "themeColor": "00FF00",
    "summary": "Filtro Curricular Deployment Test",
    "sections": [{
      "activityTitle": "✅ Test Deployment Successful",
      "activitySubtitle": "Environment: dev",
      "facts": [{
        "name": "Backend URL:",
        "value": "https://filtro-curricular-be-dev-eus.azurewebsites.net"
      }, {
        "name": "Frontend URL:",
        "value": "https://filtro-curricular-fe-dev-eus.azurewebsites.net"
      }, {
        "name": "Commit:",
        "value": "test123"
      }],
      "markdown": true
    }]
  }'
```

#### **Test Script for Multiple Environments**:
```bash
#!/bin/bash
# teams-test.sh - Test Teams notifications for all environments

WEBHOOK_URL="your-webhook-url-here"
ENVIRONMENTS=("dev" "staging" "uat" "prod")

for env in "${ENVIRONMENTS[@]}"; do
  echo "Testing notification for $env environment..."

  curl -X POST "$WEBHOOK_URL" \
    -H "Content-Type: application/json" \
    -d "{
      \"title\": \"🧪 Test Notification - $env\",
      \"text\": \"Testing Teams integration for **$env** environment\",
      \"themeColor\": \"0078D4\"
    }"

  echo "Notification sent for $env"
  sleep 2
done
```

### 3.2 Test with GitHub Actions

1. **Trigger a Workflow**:
   - Make a small change to any file in `apps/infrastructure/azure/filtro-curricular/`
   - Commit and push to `dev` branch
   - Monitor the GitHub Actions workflow

2. **Check Teams Channel**:
   - You should receive a notification in your Teams channel
   - The notification will include deployment status and details

### 3.3 Validate Webhook Response

#### **Check Response Status**:
```bash
# Test webhook and capture response
RESPONSE=$(curl -s -w "%{http_code}" -X POST "$WEBHOOK_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Response Test",
    "text": "Testing webhook response",
    "themeColor": "0078D4"
  }')

HTTP_CODE="${RESPONSE: -3}"

if [ "$HTTP_CODE" -eq 200 ]; then
  echo "✅ Webhook test successful (HTTP 200)"
else
  echo "❌ Webhook test failed (HTTP $HTTP_CODE)"
fi
```

#### **Webhook Health Check Script**:
```bash
#!/bin/bash
# webhook-health-check.sh - Monitor webhook health

WEBHOOK_URL="your-webhook-url-here"
LOG_FILE="webhook-health.log"

check_webhook() {
  local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
  local response=$(curl -s -w "%{http_code}" -X POST "$WEBHOOK_URL" \
    -H "Content-Type: application/json" \
    -d "{
      \"title\": \"Health Check\",
      \"text\": \"Webhook health check at $timestamp\",
      \"themeColor\": \"00FF00\"
    }")

  local http_code="${response: -3}"

  if [ "$http_code" -eq 200 ]; then
    echo "$timestamp - ✅ Webhook healthy (HTTP 200)" | tee -a "$LOG_FILE"
    return 0
  else
    echo "$timestamp - ❌ Webhook unhealthy (HTTP $http_code)" | tee -a "$LOG_FILE"
    return 1
  fi
}

# Run health check
check_webhook
```

## 🎨 Step 4: Customize Notification Appearance

### 4.1 Notification Format

The notifications will include:

**Success Notifications**:
- ✅ Green theme color
- Deployment environment
- Resource URLs (backend, frontend)
- Commit information
- Actor (who triggered the deployment)

**Failure Notifications**:
- ❌ Red theme color
- Error details
- Link to GitHub Actions logs
- Commit and actor information

### 4.2 Example Notification Content

```json
{
  "title": "✅ Infrastructure Deployment Success",
  "text": "**Filtro Curricular Infrastructure Deployed**\n\n**Environment:** dev\n**Resource Group:** filtro-curricular-rg-dev-eus\n**Backend URL:** https://filtro-curricular-be-dev-eus.azurewebsites.net\n**Frontend URL:** https://filtro-curricular-fe-dev-eus.azurewebsites.net\n\n**Commit:** abc123def\n**Branch:** dev\n**Actor:** your-username",
  "themeColor": "00FF00"
}
```

## 🔧 Step 5: Advanced Configuration (Optional)

### 5.1 Multiple Webhooks for Different Environments

You can set up different Teams channels for different environments:

1. **Create Environment-Specific Webhooks**:
   - Create separate channels: `#filtro-curricular-dev`, `#filtro-curricular-prod`
   - Configure webhooks for each channel

2. **Add Environment-Specific Secrets**:
   - `TEAMS_WEBHOOK_URL_DEV`
   - `TEAMS_WEBHOOK_URL_STAGING`
   - `TEAMS_WEBHOOK_URL_UAT`
   - `TEAMS_WEBHOOK_URL_PROD`

3. **Update Workflow Files** (if needed):
   - Modify the notification steps to use environment-specific webhooks

### 5.2 Rich Card Formatting

For more advanced formatting, you can use Adaptive Cards:

```json
{
  "@type": "MessageCard",
  "@context": "http://schema.org/extensions",
  "themeColor": "0078D4",
  "summary": "Filtro Curricular Deployment",
  "sections": [{
    "activityTitle": "✅ Deployment Successful",
    "activitySubtitle": "Environment: dev",
    "facts": [{
      "name": "Backend URL:",
      "value": "https://filtro-curricular-be-dev-eus.azurewebsites.net"
    }, {
      "name": "Frontend URL:",
      "value": "https://filtro-curricular-fe-dev-eus.azurewebsites.net"
    }, {
      "name": "Commit:",
      "value": "abc123def"
    }],
    "markdown": true
  }]
}
```

## 🚨 Step 6: Troubleshooting

### 6.1 Common Issues

**Problem**: "Webhook URL not working"
**Solution**:
- Verify the webhook URL is copied correctly
- Check that the webhook is still active in Teams
- Ensure the URL includes the full path with parameters

**Problem**: "No notifications received"
**Solution**:
- Check GitHub Actions logs for webhook call errors
- Verify the secret name matches exactly: `TEAMS_WEBHOOK_URL`
- Ensure the Teams channel allows connectors

**Problem**: "Notifications appear but formatting is wrong"
**Solution**:
- Check the JSON format in the workflow file
- Verify special characters are properly escaped
- Test with a simple message first

### 6.2 Webhook Validation

1. **Check Webhook Status**:
   - In Teams, go to channel settings → Connectors
   - Verify the "Filtro Curricular CI/CD" webhook is listed and active

2. **Test Webhook Manually**:
   - Use the curl command from Step 3.1
   - Check if the message appears in Teams

3. **Review GitHub Actions Logs**:
   - Go to GitHub Actions → Latest workflow run
   - Expand the notification step
   - Look for any error messages

## 📋 Step 7: Best Practices

### 7.1 Security

- **Protect Webhook URLs**: Treat webhook URLs as secrets
- **Regular Rotation**: Consider rotating webhook URLs periodically
- **Access Control**: Limit who can configure connectors in Teams

### 7.2 Notification Management

- **Avoid Spam**: Only send notifications for important events
- **Clear Messages**: Include relevant context in notifications
- **Environment Separation**: Use different channels for different environments

### 7.3 Monitoring

- **Track Delivery**: Monitor if notifications are being delivered
- **Response Times**: Check if webhook calls are timing out
- **Error Handling**: Implement fallback notification methods

## ✅ Step 8: Verification Checklist

Before completing the setup, verify:

- ✅ Teams webhook created and configured
- ✅ Webhook URL copied and stored securely
- ✅ GitHub secret `TEAMS_WEBHOOK_URL` added
- ✅ Test notification sent successfully
- ✅ Teams channel receives notifications
- ✅ Notification format appears correctly
- ✅ Both success and failure scenarios tested

## 🎯 Next Steps

1. **Test Full Pipeline**:
   - Trigger a complete deployment
   - Verify notifications for both infrastructure and application workflows

2. **Monitor Notifications**:
   - Check that all team members receive notifications
   - Adjust notification frequency if needed

3. **Customize Further**:
   - Add additional information to notifications
   - Set up environment-specific channels
   - Configure notification schedules

---

**🎉 Congratulations!** Your Microsoft Teams notifications are now configured and ready to keep your team informed about Filtro Curricular deployments!
