name: 🏗️ Filtro Curricular - Infrastructure Deployment

on:
  push:
    branches: [ main, dev ]
    paths:
      - 'apps/infrastructure/azure/filtro-curricular/**'
      - '.github/workflows/filtro-curricular-infrastructure.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'apps/infrastructure/azure/filtro-curricular/**'
      - '.github/workflows/filtro-curricular-infrastructure.yml'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - staging
          - uat
          - prod
      destroy:
        description: 'Destroy infrastructure (use with caution)'
        required: false
        default: false
        type: boolean

env:
  TERRAFORM_VERSION: '1.5.0'
  WORKING_DIRECTORY: 'apps/infrastructure/azure/filtro-curricular'

jobs:
  determine-environment:
    name: 🎯 Determine Environment
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.env.outputs.environment }}
      should_deploy: ${{ steps.env.outputs.should_deploy }}
    steps:
      - name: Determine environment and deployment
        id: env
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            echo "environment=${{ github.event.inputs.environment }}" >> $GITHUB_OUTPUT
            echo "should_deploy=true" >> $GITHUB_OUTPUT
          elif [ "${{ github.ref }}" = "refs/heads/main" ]; then
            echo "environment=staging" >> $GITHUB_OUTPUT
            echo "should_deploy=true" >> $GITHUB_OUTPUT
          elif [ "${{ github.ref }}" = "refs/heads/dev" ]; then
            echo "environment=dev" >> $GITHUB_OUTPUT
            echo "should_deploy=true" >> $GITHUB_OUTPUT
          else
            echo "environment=dev" >> $GITHUB_OUTPUT
            echo "should_deploy=false" >> $GITHUB_OUTPUT
          fi

  terraform-plan:
    name: 📋 Terraform Plan
    runs-on: ubuntu-latest
    needs: determine-environment
    environment: ${{ needs.determine-environment.outputs.environment }}
    
    defaults:
      run:
        working-directory: ${{ env.WORKING_DIRECTORY }}
    
    outputs:
      plan-exists: ${{ steps.plan.outputs.plan-exists }}
      
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
    
    - name: 🔧 Setup Terraform
      uses: hashicorp/setup-terraform@v3
      with:
        terraform_version: ${{ env.TERRAFORM_VERSION }}
    
    - name: 🔐 Azure Login
      uses: azure/login@v1
      with:
        creds: ${{ secrets.AZURE_CREDENTIALS }}
    
    - name: 📝 Set Environment Variables
      run: |
        echo "ENVIRONMENT_UPPER=$(echo '${{ needs.determine-environment.outputs.environment }}' | tr '[:lower:]' '[:upper:]')" >> $GITHUB_ENV
    
    - name: 📝 Create terraform.tfvars
      run: |
        cat > terraform.tfvars << EOF
        project_name = "${{ vars.PROJECT_NAME || 'filtro-curricular' }}"
        environment  = "${{ needs.determine-environment.outputs.environment }}"
        location     = "${{ vars.AZURE_LOCATION || 'Chile Central' }}"
        
        # OpenAI Configuration
        openai_api_key        = "${{ secrets.OPENAI_API_KEY }}"
        openai_token          = "${{ secrets.OPENAI_TOKEN }}"
        assistant_id_juridico = "${{ secrets.ASSISTANT_ID_JURIDICO }}"
        assistant_id_calidad  = "${{ secrets.ASSISTANT_ID_CALIDAD }}"
        
        # App Service Configuration
        app_service_plan_sku = "${{ vars[format('APP_SERVICE_PLAN_SKU_{0}', env.ENVIRONMENT_UPPER)] || 'B1' }}"
        acr_sku             = "${{ vars[format('ACR_SKU_{0}', env.ENVIRONMENT_UPPER)] || 'Basic' }}"

        # Scaling Configuration
        backend_min_instances  = ${{ vars[format('MIN_INSTANCES_{0}', env.ENVIRONMENT_UPPER)] || 0 }}
        backend_max_instances  = ${{ vars[format('MAX_INSTANCES_{0}', env.ENVIRONMENT_UPPER)] || 1 }}
        frontend_min_instances = ${{ vars[format('MIN_INSTANCES_{0}', env.ENVIRONMENT_UPPER)] || 0 }}
        frontend_max_instances = ${{ vars[format('MAX_INSTANCES_{0}', env.ENVIRONMENT_UPPER)] || 1 }}
        
        tags = {
          Project     = "filtro-curricular"
          Environment = "${{ needs.determine-environment.outputs.environment }}"
          ManagedBy   = "github-actions"
          Repository  = "${{ github.repository }}"
          Branch      = "${{ github.ref_name }}"
          CommitSHA   = "${{ github.sha }}"
        }
        EOF
    
    - name: 🚀 Terraform Init
      run: terraform init
    
    - name: ✅ Terraform Validate
      run: terraform validate
    
    - name: 📊 Terraform Plan
      id: plan
      run: |
        if [ "${{ github.event.inputs.destroy }}" = "true" ]; then
          terraform plan -destroy -out=tfplan
          echo "plan-exists=true" >> $GITHUB_OUTPUT
        else
          terraform plan -out=tfplan
          echo "plan-exists=true" >> $GITHUB_OUTPUT
        fi
    
    - name: 📤 Upload Terraform Plan
      if: steps.plan.outputs.plan-exists == 'true'
      uses: actions/upload-artifact@v4
      with:
        name: terraform-plan-${{ needs.determine-environment.outputs.environment }}
        path: ${{ env.WORKING_DIRECTORY }}/tfplan
        retention-days: 1

  terraform-apply:
    name: 🚀 Terraform Apply
    runs-on: ubuntu-latest
    needs: [determine-environment, terraform-plan]
    if: needs.determine-environment.outputs.should_deploy == 'true' && needs.terraform-plan.outputs.plan-exists == 'true'
    environment: ${{ needs.determine-environment.outputs.environment }}
    
    defaults:
      run:
        working-directory: ${{ env.WORKING_DIRECTORY }}
    
    outputs:
      acr_name: ${{ steps.terraform-output.outputs.acr_name }}
      acr_login_server: ${{ steps.terraform-output.outputs.acr_login_server }}
      resource_group_name: ${{ steps.terraform-output.outputs.resource_group_name }}
      backend_app_name: ${{ steps.terraform-output.outputs.backend_app_name }}
      frontend_app_name: ${{ steps.terraform-output.outputs.frontend_app_name }}
      backend_url: ${{ steps.terraform-output.outputs.backend_url }}
      frontend_url: ${{ steps.terraform-output.outputs.frontend_url }}
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
    
    - name: 🔧 Setup Terraform
      uses: hashicorp/setup-terraform@v3
      with:
        terraform_version: ${{ env.TERRAFORM_VERSION }}
        terraform_wrapper: false
    
    - name: 🔐 Azure Login
      uses: azure/login@v1
      with:
        creds: ${{ secrets.AZURE_CREDENTIALS }}
    
    - name: 📝 Set Environment Variables
      run: |
        echo "ENVIRONMENT_UPPER=$(echo '${{ needs.determine-environment.outputs.environment }}' | tr '[:lower:]' '[:upper:]')" >> $GITHUB_ENV
    
    - name: 📝 Create terraform.tfvars
      run: |
        cat > terraform.tfvars << EOF
        project_name = "${{ vars.PROJECT_NAME || 'filtro-curricular' }}"
        environment  = "${{ needs.determine-environment.outputs.environment }}"
        location     = "${{ vars.AZURE_LOCATION || 'Chile Central' }}"
        
        # OpenAI Configuration
        openai_api_key        = "${{ secrets.OPENAI_API_KEY }}"
        openai_token          = "${{ secrets.OPENAI_TOKEN }}"
        assistant_id_juridico = "${{ secrets.ASSISTANT_ID_JURIDICO }}"
        assistant_id_calidad  = "${{ secrets.ASSISTANT_ID_CALIDAD }}"
        
        # App Service Configuration
        app_service_plan_sku = "${{ vars[format('APP_SERVICE_PLAN_SKU_{0}', env.ENVIRONMENT_UPPER)] || 'B1' }}"
        acr_sku             = "${{ vars[format('ACR_SKU_{0}', env.ENVIRONMENT_UPPER)] || 'Basic' }}"

        # Scaling Configuration
        backend_min_instances  = ${{ vars[format('MIN_INSTANCES_{0}', env.ENVIRONMENT_UPPER)] || 0 }}
        backend_max_instances  = ${{ vars[format('MAX_INSTANCES_{0}', env.ENVIRONMENT_UPPER)] || 1 }}
        frontend_min_instances = ${{ vars[format('MIN_INSTANCES_{0}', env.ENVIRONMENT_UPPER)] || 0 }}
        frontend_max_instances = ${{ vars[format('MAX_INSTANCES_{0}', env.ENVIRONMENT_UPPER)] || 1 }}
        
        tags = {
          Project     = "filtro-curricular"
          Environment = "${{ needs.determine-environment.outputs.environment }}"
          ManagedBy   = "github-actions"
          Repository  = "${{ github.repository }}"
          Branch      = "${{ github.ref_name }}"
          CommitSHA   = "${{ github.sha }}"
        }
        EOF
    
    - name: 📥 Download Terraform Plan
      uses: actions/download-artifact@v4
      with:
        name: terraform-plan-${{ needs.determine-environment.outputs.environment }}
        path: ${{ env.WORKING_DIRECTORY }}
    
    - name: 🚀 Terraform Init
      run: terraform init
    
    - name: ⚡ Terraform Apply
      run: terraform apply tfplan
    
    - name: 📊 Get Terraform Outputs
      id: terraform-output
      if: github.event.inputs.destroy != 'true'
      run: |
        echo "acr_name=$(terraform output -raw container_registry_name)" >> $GITHUB_OUTPUT
        echo "acr_login_server=$(terraform output -raw container_registry_login_server)" >> $GITHUB_OUTPUT
        echo "resource_group_name=$(terraform output -raw resource_group_name)" >> $GITHUB_OUTPUT
        echo "backend_app_name=$(terraform output -raw backend_app_service_name)" >> $GITHUB_OUTPUT
        echo "frontend_app_name=$(terraform output -raw frontend_app_service_name)" >> $GITHUB_OUTPUT
        echo "backend_url=$(terraform output -raw backend_app_service_url)" >> $GITHUB_OUTPUT
        echo "frontend_url=$(terraform output -raw frontend_app_service_url)" >> $GITHUB_OUTPUT

  notify-success:
    name: 📧 Notify Success
    runs-on: ubuntu-latest
    needs: [determine-environment, terraform-apply]
    if: success()
    
    steps:
    - name: 📝 Placeholder Step
      run: echo "Notifications are currently disabled. Configure SMTP and Teams webhook secrets to enable notifications."

    # TODO: Re-enable email notifications once SMTP secrets are configured
    # - name: 📧 Send Email Notification
    #   if: vars.NOTIFICATION_EMAIL
    #   uses: dawidd6/action-send-mail@v3
    #   with:
    #     server_address: ${{ vars.SMTP_SERVER || 'smtp.office365.com' }}
    #     server_port: ${{ vars.SMTP_PORT || '587' }}
    #     username: ${{ secrets.SMTP_USERNAME }}
    #     password: ${{ secrets.SMTP_PASSWORD }}
    #     subject: "✅ Filtro Curricular Infrastructure Deployed - ${{ needs.determine-environment.outputs.environment }}"
    #     to: ${{ vars.NOTIFICATION_EMAIL }}
    #     from: ${{ secrets.SMTP_USERNAME }}
    #     body: |
    #       Infrastructure deployment completed successfully!
    #
    #       Environment: ${{ needs.determine-environment.outputs.environment }}
    #       Resource Group: ${{ needs.terraform-apply.outputs.resource_group_name }}
    #       Backend URL: ${{ needs.terraform-apply.outputs.backend_url }}
    #       Frontend URL: ${{ needs.terraform-apply.outputs.frontend_url }}
    #
    #       Commit: ${{ github.sha }}
    #       Branch: ${{ github.ref_name }}
    #       Actor: ${{ github.actor }}

    # TODO: Re-enable Teams notifications once webhook URL is configured
    # - name: 📢 Send Teams Notification
    #   if: secrets.TEAMS_WEBHOOK_URL
    #   uses: skitionek/notify-microsoft-teams@master
    #   with:
    #     webhook_url: ${{ secrets.TEAMS_WEBHOOK_URL }}
    #     title: "✅ Infrastructure Deployment Success"
    #     message: |
    #       **Filtro Curricular Infrastructure Deployed**
    #
    #       **Environment:** ${{ needs.determine-environment.outputs.environment }}
    #       **Resource Group:** ${{ needs.terraform-apply.outputs.resource_group_name }}
    #       **Backend URL:** ${{ needs.terraform-apply.outputs.backend_url }}
    #       **Frontend URL:** ${{ needs.terraform-apply.outputs.frontend_url }}
    #
    #       **Commit:** ${{ github.sha }}
    #       **Branch:** ${{ github.ref_name }}
    #       **Actor:** ${{ github.actor }}

  notify-failure:
    name: 🚨 Notify Failure
    runs-on: ubuntu-latest
    needs: [determine-environment, terraform-plan, terraform-apply]
    if: failure()
    
    steps:
    - name: 📝 Placeholder Step
      run: echo "Notifications are currently disabled. Configure SMTP and Teams webhook secrets to enable notifications."

    # TODO: Re-enable email notifications once SMTP secrets are configured
    # - name: 📧 Send Email Notification
    #   if: vars.NOTIFICATION_EMAIL
    #   uses: dawidd6/action-send-mail@v3
    #   with:
    #     server_address: ${{ vars.SMTP_SERVER || 'smtp.office365.com' }}
    #     server_port: ${{ vars.SMTP_PORT || '587' }}
    #     username: ${{ secrets.SMTP_USERNAME }}
    #     password: ${{ secrets.SMTP_PASSWORD }}
    #     subject: "❌ Filtro Curricular Infrastructure Deployment Failed - ${{ needs.determine-environment.outputs.environment }}"
    #     to: ${{ vars.NOTIFICATION_EMAIL }}
    #     from: ${{ secrets.SMTP_USERNAME }}
    #     body: |
    #       Infrastructure deployment failed!
    #
    #       Environment: ${{ needs.determine-environment.outputs.environment }}
    #
    #       Please check the GitHub Actions logs for details.
    #
    #       Commit: ${{ github.sha }}
    #       Branch: ${{ github.ref_name }}
    #       Actor: ${{ github.actor }}

    # TODO: Re-enable Teams notifications once webhook URL is configured
    # - name: 📢 Send Teams Notification
    #   if: secrets.TEAMS_WEBHOOK_URL
    #   uses: skitionek/notify-microsoft-teams@master
    #   with:
    #     webhook_url: ${{ secrets.TEAMS_WEBHOOK_URL }}
    #     title: "❌ Infrastructure Deployment Failed"
    #     message: |
    #       **Filtro Curricular Infrastructure Deployment Failed**
    #
    #       **Environment:** ${{ needs.determine-environment.outputs.environment }}
    #
    #       Please check the GitHub Actions logs for details.
    #
    #       **Commit:** ${{ github.sha }}
    #       **Branch:** ${{ github.ref_name }}
    #       **Actor:** ${{ github.actor }}
