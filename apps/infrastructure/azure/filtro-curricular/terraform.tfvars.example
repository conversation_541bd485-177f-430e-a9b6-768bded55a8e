# Project Configuration
project_name = "filtro-curricular"
environment  = "dev"
location     = "Chile Central"

# Tags
tags = {
  Project     = "filtro-curricular"
  Environment = "dev"
  ManagedBy   = "terraform"
  Owner       = "your-team"
}

# Azure Container Registry
acr_sku = "Basic"  # Options: Basic, Standard, Premium

# App Service Plan
app_service_plan_sku = "B1"  # Options: B1, B2, B3, S1, S2, S3, P1v2, P2v2, P3v2

# Application Configuration
backend_image_tag  = "latest"
frontend_image_tag = "latest"
backend_port       = 5000
frontend_port      = 80

# OpenAI Configuration (REQUIRED - Set these values)
openai_api_key        = "your-openai-api-key-here"
openai_token          = "your-openai-token-here"
assistant_id_juridico = "your-assistant-id-juridico-here"
assistant_id_calidad  = "your-assistant-id-calidad-here"

# Storage Account
storage_account_tier             = "Standard"
storage_account_replication_type = "LRS"  # Options: LRS, GRS, RAGRS, ZRS, GZRS, RAGZRS

# Scaling Configuration
backend_min_instances  = 1
backend_max_instances  = 3
frontend_min_instances = 1
frontend_max_instances = 3

# Networking (Optional)
enable_vnet        = false  # Set to true for VNet integration
enable_app_gateway = false  # Set to true for Application Gateway

# Custom Domain (Optional)
custom_domain        = ""  # e.g., "yourdomain.com"
ssl_certificate_path = ""  # Path to SSL certificate file
