# Create ECR Repository for cgnsrch-chatbot-fe-ionic
resource "aws_ecr_repository" "cgnsrch_chatbot_fe_ionic" {
  name = "cgnsrch-chatbot-fe-ionic"

  image_scanning_configuration {
    scan_on_push = true  # Enable image scanning on push
  }

  image_tag_mutability = "MUTABLE" # or "IMMUTABLE" depending on your tag strategy

  tags = {
    Name        = "cgnsrch-chatbot-fe-ionic"
    Environment = "dev" # or "prod", "staging", etc.
    Project     = "cgnsrch-chatbot"
    Team        = "your-team" #replace with the name of the team responsible for the repository
  }
}

# Create ECR Repository for cgnsrch-chatbot-be-pfapi
resource "aws_ecr_repository" "cgnsrch_chatbot_be_pfapi" {
  name = "cgnsrch-chatbot-be-pfapi"

  image_scanning_configuration {
    scan_on_push = true  # Enable image scanning on push
  }

  image_tag_mutability = "MUTABLE" # or "IMMUTABLE" depending on your tag strategy

  tags = {
    Name        = "cgnsrch-chatbot-be-pfapi"
    Environment = "dev" # or "prod", "staging", etc.
    Project     = "cgnsrch-chatbot"
    Team        = "your-team" #replace with the name of the team responsible for the repository
  }
}