# Filtro Curricular - Azure Infrastructure

This directory contains Terraform configuration for deploying the Filtro Curricular applications to Microsoft Azure using automated CI/CD with GitHub Actions.

## 🏗️ Architecture Overview

The infrastructure includes:

- **Azure Container Registry (ACR)**: Stores Docker images for both applications
- **Azure App Service**: Hosts both backend (FastAPI) and frontend (Angular/Ionic) applications
- **Azure Key Vault**: Manages secrets and sensitive configuration
- **Azure Storage Account**: Handles file uploads and application data
- **Azure Application Insights**: Provides monitoring and logging
- **Azure Log Analytics**: Centralized logging workspace

## 📱 Applications

1. **Backend API** (`filtro-curricular-be-api`): Python FastAPI application
2. **Frontend Web** (`filtro-curricular-fe-web`): Angular/Ionic web application

## 🚀 Deployment Methods

### 🎯 Primary Method: GitHub Actions (Recommended)

**Automated CI/CD deployment with GitHub Actions is the recommended approach.**

#### Quick Setup:
1. **Configure GitHub Secrets**: Follow the [GitHub Actions Setup Guide](GITHUB_ACTIONS_SETUP.md)
2. **Push to Deploy**:
   - Push to `dev` branch → Deploys to dev environment
   - Push to `main` branch → Deploys to staging environment
   - Manual deployment to uat/prod via GitHub Actions UI

#### Benefits:
- ✅ Fully automated deployment
- ✅ Environment-specific configurations
- ✅ Email and Teams notifications
- ✅ Rollback capabilities
- ✅ Security best practices
- ✅ Branch protection rules

**📚 Setup Guides**:
- **🌐 Azure Portal Setup**: [AZURE_PORTAL_SETUP_GUIDE.md](AZURE_PORTAL_SETUP_GUIDE.md) - Web-based configuration
- **⚙️ GitHub Actions Setup**: [GITHUB_ACTIONS_SETUP.md](GITHUB_ACTIONS_SETUP.md) - Complete CI/CD guide
- **📢 Teams Notifications**: [TEAMS_WEBHOOK_SETUP.md](TEAMS_WEBHOOK_SETUP.md) - Teams integration
- **✅ Deployment Checklist**: [DEPLOYMENT_CHECKLIST.md](DEPLOYMENT_CHECKLIST.md) - Complete verification

### 🛠️ Alternative Method: Manual Deployment

For development and testing purposes, you can still deploy manually:

#### Prerequisites

1. **Azure CLI**: Install and configure Azure CLI
   ```bash
   az login
   az account set --subscription "your-subscription-id"
   ```

2. **Terraform**: Install Terraform >= 1.0
   ```bash
   terraform version
   ```

3. **Docker**: For building and pushing container images
   ```bash
   docker --version
   ```

#### Manual Deployment Steps

1. **Configure Variables**:
   ```bash
   cp terraform.tfvars.example terraform.tfvars
   # Edit terraform.tfvars with your values
   ```

2. **Deploy Infrastructure**:
   ```bash
   terraform init
   terraform plan
   terraform apply
   ```

3. **Deploy Applications**:
   ```bash
   ./deploy.sh
   ```

**📚 Detailed Manual Guide**: [QUICK_START.md](QUICK_START.md)

## Configuration

### Environment Variables

The backend application uses the following environment variables (automatically configured):

- `GPT_API_KEY`: OpenAI API key (from Key Vault)
- `OPENAI_TOKEN`: OpenAI token (from Key Vault)
- `ASSISTANT_ID_JURIDICO`: OpenAI Assistant ID for legal queries
- `ASSISTANT_ID_CALIDAD`: OpenAI Assistant ID for quality queries
- `BUCKET_NAME`: Azure Storage container name for uploads

### Scaling

Configure auto-scaling by modifying variables:

```hcl
backend_min_instances  = 1
backend_max_instances  = 5
frontend_min_instances = 1
frontend_max_instances = 3
```

### Networking (Optional)

Enable VNet integration for enhanced security:

```hcl
enable_vnet = true
```

Enable Application Gateway for advanced load balancing:

```hcl
enable_app_gateway = true
```

## Monitoring

### Application Insights

Monitor application performance and errors:

1. Go to Azure Portal → Application Insights
2. Find your Application Insights resource: `filtro-curricular-insights-{environment}`
3. View metrics, logs, and performance data

### Log Analytics

View centralized logs:

1. Go to Azure Portal → Log Analytics workspaces
2. Find your workspace: `filtro-curricular-logs-{environment}`
3. Query logs using KQL (Kusto Query Language)

## Security

### Key Vault

Secrets are stored in Azure Key Vault:

- OpenAI API credentials
- Storage account keys
- Application secrets

### Managed Identity

App Services use system-assigned managed identities to access:

- Azure Container Registry (for pulling images)
- Azure Key Vault (for accessing secrets)
- Azure Storage (for file operations)

## Troubleshooting

### Common Issues

1. **ACR Authentication**: Ensure App Service has AcrPull role assignment
2. **Key Vault Access**: Verify managed identity has proper access policies
3. **Container Startup**: Check App Service logs for container startup issues

### Useful Commands

```bash
# View App Service logs
az webapp log tail --name <app-name> --resource-group <rg-name>

# Check container registry repositories
az acr repository list --name <acr-name>

# Test Key Vault access
az keyvault secret show --vault-name <kv-name> --name <secret-name>
```

## Cleanup

To destroy all resources:

```bash
terraform destroy
```

**Warning**: This will permanently delete all resources and data.

## Support

For issues or questions:

1. Check Azure App Service logs
2. Review Application Insights for errors
3. Verify Terraform state and configuration
4. Consult Azure documentation for specific services
