# Configure Route 53 Hosted Zone

data "aws_route53_zone" "selected" {
  name         = "cognisearch.cl."
}

resource "aws_route53_record" "dev_backend" {
  zone_id = data.aws_route53_zone.selected.zone_id 
  name    = "dev.backend.${data.aws_route53_zone.selected.name}"
  type    = "CNAME"
  ttl     = "60"
  records = [aws_lb.cognisearch_alb.dns_name]
}

resource "aws_route53_record" "dev_frontend" {
  zone_id = data.aws_route53_zone.selected.zone_id 
  name    = "dev.frontend.${data.aws_route53_zone.selected.name}"
  type    = "CNAME"
  ttl     = "60"
  records = [aws_lb.cognisearch_alb.dns_name]
}