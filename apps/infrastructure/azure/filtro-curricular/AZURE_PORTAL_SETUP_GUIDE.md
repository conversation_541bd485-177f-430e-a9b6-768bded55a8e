# 🌐 Azure Portal Setup Guide for Filtro Curricular CI/CD

This comprehensive guide walks you through setting up Azure Service Principal and all necessary configurations using both the Azure portal web interface and Azure CLI commands.

## 📋 Prerequisites

- Access to Azure portal (portal.azure.com)
- Azure subscription with Owner or User Access Administrator permissions
- GitHub repository access for the ragtech project
- Azure CLI installed (for CLI alternatives)

## 🔧 Azure CLI Installation and Setup

### Install Azure CLI

#### **Windows**

```powershell
# Using winget
winget install -e --id Microsoft.AzureCLI

# Using MSI installer
# Download from: https://aka.ms/installazurecliwindows
```

#### **macOS**

```bash
# Using Homebrew
brew update && brew install azure-cli

# Using curl
curl -L https://aka.ms/InstallAzureCli | bash
```

#### **Linux (Ubuntu/Debian)**

```bash
# Update package index
sudo apt-get update

# Install required packages
sudo apt-get install ca-certificates curl apt-transport-https lsb-release gnupg

# Download and install Microsoft signing key
curl -sL https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor | sudo tee /etc/apt/trusted.gpg.d/microsoft.gpg > /dev/null

# Add Azure CLI repository
AZ_REPO=$(lsb_release -cs)
echo "deb [arch=amd64] https://packages.microsoft.com/repos/azure-cli/ $AZ_REPO main" | sudo tee /etc/apt/sources.list.d/azure-cli.list

# Update package index and install
sudo apt-get update
sudo apt-get install azure-cli
```

### Initial Azure CLI Authentication

```bash
# Login to Azure
az login

# List available subscriptions
az account list --output table

# Set your subscription (replace with your subscription ID)
az account set --subscription "your-subscription-id"

# Verify current subscription
az account show
```

## 🎯 Step 1: Azure Subscription Setup

### 1.1 Identify Your Subscription

#### **Web Method (Azure Portal)**

1. **Navigate to Azure Portal**: Go to [portal.azure.com](https://portal.azure.com)
2. **Open Subscriptions**:
   - Click on "Subscriptions" in the left sidebar
   - Or search "Subscriptions" in the top search bar
3. **Select Your Subscription**:
   - Click on the subscription you want to use
   - **Copy the Subscription ID** - you'll need this later
   - Note: The Subscription ID format looks like: `********-1234-1234-1234-************` 2ee03089-129c-4624-97c9-990d968e6141

#### **CLI Alternative**

```bash
# List all available subscriptions
az account list --output table

# Get current subscription details
az account show --output table

# Set specific subscription (if you have multiple)
az account set --subscription "your-subscription-name-or-id"

# Get subscription ID for use in scripts
SUBSCRIPTION_ID=$(az account show --query id --output tsv)
echo "Subscription ID: $SUBSCRIPTION_ID"
```

### 1.2 Verify Subscription Permissions

#### **Web Method (Azure Portal)**

1. **Check Your Role**:
   - In your subscription, click "Access control (IAM)" in the left menu
   - Click "View my access"
   - Ensure you have "Owner" or "User Access Administrator" role
   - If not, contact your Azure administrator

#### **CLI Alternative**

```bash
# Check your role assignments in the current subscription
az role assignment list --assignee $(az account show --query user.name --output tsv) --output table

# Check if you have Owner or User Access Administrator role
az role assignment list \
  --assignee $(az account show --query user.name --output tsv) \
  --query "[?roleDefinitionName=='Owner' || roleDefinitionName=='User Access Administrator']" \
  --output table
```

### 1.3 Enable Required Resource Providers

#### **Web Method (Azure Portal)**

1. **Navigate to Resource Providers**:
   - In your subscription, click "Resource providers" in the left menu
2. **Register Required Providers**:
   - Search and ensure these are "Registered":
     - `Microsoft.Web` (for App Services)
     - `Microsoft.ContainerRegistry` (for ACR)
     - `Microsoft.KeyVault` (for Key Vault)
     - `Microsoft.Storage` (for Storage Accounts)
     - `Microsoft.Insights` (for Application Insights)
   - If any show "NotRegistered", click on them and select "Register"

#### **CLI Alternative**

```bash
# List all resource providers and their registration status
az provider list --output table

# Check specific providers needed for the project
REQUIRED_PROVIDERS=(
  "Microsoft.Web"
  "Microsoft.ContainerRegistry"
  "Microsoft.KeyVault"
  "Microsoft.Storage"
  "Microsoft.Insights"
)

# Check registration status of required providers
for provider in "${REQUIRED_PROVIDERS[@]}"; do
  echo "Checking $provider..."
  az provider show --namespace "$provider" --query "registrationState" --output tsv
done

# Register all required providers
for provider in "${REQUIRED_PROVIDERS[@]}"; do
  echo "Registering $provider..."
  az provider register --namespace "$provider"
done

# Verify registration (may take a few minutes)
for provider in "${REQUIRED_PROVIDERS[@]}"; do
  echo "Verifying $provider..."
  az provider show --namespace "$provider" --query "{Namespace:namespace, State:registrationState}" --output table
done
```

## 🔐 Step 2: Create Service Principal

### 2.1 Navigate to App Registrations

#### **Web Method (Azure Portal)**

1. **Open Azure Active Directory**:
   - Search "Azure Active Directory" in the top search bar
   - Click on "0
   2 Azure Active Directory"
2. **Access App Registrations**:
   - In the left menu, click "App registrations"
   - Click "New registration"

#### **CLI Alternative**

```bash
# Set variables for consistency
SP_NAME="sp-filtro-curricular-github-actions"
SUBSCRIPTION_ID=$(az account show --query id --output tsv)

# Create Service Principal with Contributor role
echo "Creating Service Principal: $SP_NAME"
SP_OUTPUT=$(az ad sp create-for-rbac \
  --name "$SP_NAME" \
  --role "Contributor" \
  --scopes "/subscriptions/$SUBSCRIPTION_ID" \
  --sdk-auth)

echo "Service Principal created successfully!"
echo "Save this output securely (you'll need it for GitHub secrets):"
echo "$SP_OUTPUT"

# Extract individual values for reference
CLIENT_ID=$(echo "$SP_OUTPUT" | jq -r '.clientId')
CLIENT_SECRET=$(echo "$SP_OUTPUT" | jq -r '.clientSecret')
TENANT_ID=$(echo "$SP_OUTPUT" | jq -r '.tenantId')

echo ""
echo "Individual values:"
echo "Client ID: $CLIENT_ID"
echo "Tenant ID: $TENANT_ID"
echo "Client Secret: [HIDDEN - see full output above]"
```

### 2.2 Register New Application

#### **Web Method (Azure Portal)**

1. **Fill Application Details**:
   - **Name**: `sp-filtro-curricular-github-actions`
   - **Supported account types**: Select "Accounts in this organizational directory only"
   - **Redirect URI**: Leave blank
   - Click "Register"

2. **Copy Application Details**:
   - After creation, you'll see the "Overview" page
   - **Copy and save these values**:
     - **Application (client) ID**: This is your `clientId`
     - **Directory (tenant) ID**: This is your `tenantId`

#### **CLI Alternative**

```bash
# If you used the CLI method above, the application is already created
# You can verify it exists with:
az ad sp list --display-name "$SP_NAME" --output table

# Get the Service Principal details
SP_OBJECT_ID=$(az ad sp list --display-name "$SP_NAME" --query "[0].id" --output tsv)
echo "Service Principal Object ID: $SP_OBJECT_ID"
```

### 2.3 Create Client Secret

#### **Web Method (Azure Portal)**

1. **Navigate to Certificates & secrets**:
   - In the left menu, click "Certificates & secrets"
   - Click "New client secret"

2. **Create Secret**:
   - **Description**: `GitHub Actions Secret`
   - **Expires**: Select "24 months" (or your organization's policy)
   - Click "Add"

3. **Copy Secret Value**:
   - **IMPORTANT**: Copy the secret **Value** immediately (not the Secret ID)
   - This is your `clientSecret` - you cannot retrieve it later
   - Store it securely

#### **CLI Alternative**

```bash
# If you used the CLI method above, the secret is already created
# The secret was included in the SP_OUTPUT from step 2.1

# If you need to create a new secret for an existing Service Principal:
APP_ID=$(az ad sp list --display-name "$SP_NAME" --query "[0].appId" --output tsv)

# Create new client secret (if needed)
NEW_SECRET=$(az ad app credential reset --id "$APP_ID" --append --display-name "GitHub Actions Secret" --years 2 --query password --output tsv)
echo "New client secret created: $NEW_SECRET"
echo "IMPORTANT: Save this secret immediately - you cannot retrieve it later!"
```

## 🔑 Step 3: Assign Permissions to Service Principal

### 3.1 Assign Subscription-Level Roles

#### **Web Method (Azure Portal)**

1. **Navigate to Subscriptions**:
   - Go back to "Subscriptions" in the main Azure portal
   - Click on your subscription

2. **Open Access Control**:
   - Click "Access control (IAM)" in the left menu
   - Click "Add" → "Add role assignment"

3. **Assign Contributor Role**:
   - **Role**: Search and select "Contributor"
   - Click "Next"
   - **Assign access to**: Select "User, group, or service principal"
   - Click "Select members"
   - Search for `sp-filtro-curricular-github-actions`
   - Select your service principal and click "Select"
   - Click "Review + assign" → "Assign"

4. **Assign Key Vault Administrator Role**:
   - Repeat the above process with:
   - **Role**: "Key Vault Administrator"
   - Same service principal selection

5. **Assign User Access Administrator Role**:
   - Repeat the above process with:
   - **Role**: "User Access Administrator"
   - Same service principal selection

#### **CLI Alternative**

```bash
# Set variables (use the same from previous steps)
SP_NAME="sp-filtro-curricular-github-actions"
SUBSCRIPTION_ID=$(az account show --query id --output tsv)

# Get Service Principal Object ID
SP_OBJECT_ID=$(az ad sp list --display-name "$SP_NAME" --query "[0].id" --output tsv)
echo "Service Principal Object ID: $SP_OBJECT_ID"

# Assign Contributor role
echo "Assigning Contributor role..."
az role assignment create \
  --assignee "$SP_OBJECT_ID" \
  --role "Contributor" \
  --scope "/subscriptions/$SUBSCRIPTION_ID"

# Assign Key Vault Administrator role
echo "Assigning Key Vault Administrator role..."
az role assignment create \
  --assignee "$SP_OBJECT_ID" \
  --role "Key Vault Administrator" \
  --scope "/subscriptions/$SUBSCRIPTION_ID"

# Assign User Access Administrator role
echo "Assigning User Access Administrator role..."
az role assignment create \
  --assignee "$SP_OBJECT_ID" \
  --role "User Access Administrator" \
  --scope "/subscriptions/$SUBSCRIPTION_ID"

echo "All role assignments completed successfully!"
```

### 3.2 Verify Role Assignments

#### **Web Method (Azure Portal)**

1. **Check Assignments**:
   - In the subscription's "Access control (IAM)"
   - Click "Role assignments" tab
   - Search for your service principal name
   - Verify all three roles are assigned:
     - Contributor
     - Key Vault Administrator
     - User Access Administrator

#### **CLI Alternative**

```bash
# Verify all role assignments for the Service Principal
echo "Verifying role assignments for: $SP_NAME"
az role assignment list \
  --assignee "$SP_OBJECT_ID" \
  --scope "/subscriptions/$SUBSCRIPTION_ID" \
  --output table

# Check for specific required roles
REQUIRED_ROLES=("Contributor" "Key Vault Administrator" "User Access Administrator")

echo ""
echo "Checking for required roles:"
for role in "${REQUIRED_ROLES[@]}"; do
  assignment=$(az role assignment list \
    --assignee "$SP_OBJECT_ID" \
    --scope "/subscriptions/$SUBSCRIPTION_ID" \
    --query "[?roleDefinitionName=='$role']" \
    --output tsv)

  if [ -n "$assignment" ]; then
    echo "✅ $role: Assigned"
  else
    echo "❌ $role: NOT assigned"
  fi
done
```

## 📦 Step 4: Set Up Terraform Remote State Backend

### 4.1 Create Storage Account for Terraform State

#### **Web Method (Azure Portal)**

1. **Create Resource Group**:
   - Search "Resource groups" in Azure portal
   - Click "Create"
   - **Resource group name**: `filtro-curricular-terraform-state-rg`
   - **Region**: `Chile Central` (or your preferred region)
   - Click "Review + create" → "Create"

2. **Create Storage Account**:
   - Search "Storage accounts" in Azure portal
   - Click "Create"
   - **Resource group**: Select `filtro-curricular-terraform-state-rg`
   - **Storage account name**: `filtrocurriculartfstate` (must be globally unique)
   - **Region**: Same as resource group
   - **Performance**: Standard
   - **Redundancy**: LRS (Locally-redundant storage)
   - Click "Review + create" → "Create"

3. **Create Container for State Files**:
   - Open your new storage account
   - In left menu, click "Containers" under "Data storage"
   - Click "+ Container"
   - **Name**: `terraform-state`
   - **Public access level**: Private
   - Click "Create"

#### **CLI Alternative**

```bash
# Set variables for consistency
RESOURCE_GROUP_NAME="filtro-curricular-terraform-state-rg"
STORAGE_ACCOUNT_NAME="filtrocurriculartfstate$(date +%s)"  # Add timestamp for uniqueness
LOCATION="Chile Central"
CONTAINER_NAME="terraform-state"

# Create resource group for Terraform state
echo "Creating resource group: $RESOURCE_GROUP_NAME"
az group create \
  --name "$RESOURCE_GROUP_NAME" \
  --location "$LOCATION"

# Create storage account
echo "Creating storage account: $STORAGE_ACCOUNT_NAME"
az storage account create \
  --name "$STORAGE_ACCOUNT_NAME" \
  --resource-group "$RESOURCE_GROUP_NAME" \
  --location "$LOCATION" \
  --sku "Standard_LRS" \
  --kind "StorageV2" \
  --access-tier "Hot"

# Get storage account key
STORAGE_KEY=$(az storage account keys list \
  --resource-group "$RESOURCE_GROUP_NAME" \
  --account-name "$STORAGE_ACCOUNT_NAME" \
  --query "[0].value" \
  --output tsv)

# Create container for Terraform state
echo "Creating container: $CONTAINER_NAME"
az storage container create \
  --name "$CONTAINER_NAME" \
  --account-name "$STORAGE_ACCOUNT_NAME" \
  --account-key "$STORAGE_KEY" \
  --public-access off

echo ""
echo "✅ Terraform state backend created successfully!"
echo "Storage Account Name: $STORAGE_ACCOUNT_NAME"
echo "Container Name: $CONTAINER_NAME"
echo "Resource Group: $RESOURCE_GROUP_NAME"
```

### 4.2 Configure Storage Account Access

#### **Web Method (Azure Portal)**

1. **Get Storage Account Details**:
   - In your storage account, click "Access keys" in left menu
   - **Copy and save**:
     - **Storage account name**: e.g., `filtrocurriculartfstate`
     - **Key1**: Copy the key value

2. **Assign Storage Permissions to Service Principal**:
   - In the storage account, click "Access control (IAM)"
   - Click "Add" → "Add role assignment"
   - **Role**: "Storage Blob Data Contributor"
   - **Assign access to**: "User, group, or service principal"
   - Select your service principal: `sp-filtro-curricular-github-actions`
   - Click "Review + assign" → "Assign"

#### **CLI Alternative**

```bash
# Get Service Principal Object ID (from previous steps)
SP_NAME="sp-filtro-curricular-github-actions"
SP_OBJECT_ID=$(az ad sp list --display-name "$SP_NAME" --query "[0].id" --output tsv)

# Assign Storage Blob Data Contributor role to Service Principal
echo "Assigning Storage Blob Data Contributor role to Service Principal..."
az role assignment create \
  --assignee "$SP_OBJECT_ID" \
  --role "Storage Blob Data Contributor" \
  --scope "/subscriptions/$(az account show --query id --output tsv)/resourceGroups/$RESOURCE_GROUP_NAME/providers/Microsoft.Storage/storageAccounts/$STORAGE_ACCOUNT_NAME"

# Verify the role assignment
echo "Verifying storage account permissions..."
az role assignment list \
  --assignee "$SP_OBJECT_ID" \
  --scope "/subscriptions/$(az account show --query id --output tsv)/resourceGroups/$RESOURCE_GROUP_NAME/providers/Microsoft.Storage/storageAccounts/$STORAGE_ACCOUNT_NAME" \
  --output table

echo ""
echo "✅ Storage account permissions configured successfully!"
echo ""
echo "📝 Save these values for your backend.tf configuration:"
echo "Resource Group: $RESOURCE_GROUP_NAME"
echo "Storage Account: $STORAGE_ACCOUNT_NAME"
echo "Container: $CONTAINER_NAME"
```

## 🔗 Step 5: GitHub Integration

### 5.1 Prepare Azure Credentials JSON

Create a JSON object with your collected values:

```json
{
  "clientId": "your-application-client-id",
  "clientSecret": "your-client-secret-value",
  "subscriptionId": "your-subscription-id",
  "tenantId": "your-directory-tenant-id"
}
```


### 5.2 Add GitHub Repository Secrets

1. **Navigate to GitHub Repository**:
   - Go to your ragtech repository on GitHub
   - Click "Settings" tab
   - Click "Secrets and variables" → "Actions"

2. **Add Repository Secrets**:
   Click "New repository secret" for each:

   **AZURE_CREDENTIALS**:

   ```json
   {
     "clientId": "your-application-client-id",
     "clientSecret": "your-client-secret-value", 
     "subscriptionId": "your-subscription-id",
     "tenantId": "your-directory-tenant-id"
   }
   ```

   {
  "clientId": "fa81931f-6772-4881-9736-235161dca6af",
  "clientSecret": "****************************************",
  "subscriptionId": "2ee03089-129c-4624-97c9-990d968e6141",
  "tenantId": "bd5360f9-4c71-4f42-8309-9de054b4b337"
}

   **OPENAI_API_KEY**:

   ```
   sk-your-openai-api-key-here
   ```

   **OPENAI_TOKEN**:

   ```
   your-openai-token-here
   ```

   **ASSISTANT_ID_JURIDICO**:

   ```
   asst_your-juridico-assistant-id
   ```

   **ASSISTANT_ID_CALIDAD**:

   ```
   asst_your-calidad-assistant-id
   ```

   **NOTIFICATION_EMAIL**:

   ```
   <EMAIL>
   ```

   **TEAMS_WEBHOOK_URL** (if using Teams):

   ```
   https://your-company.webhook.office.com/webhookb2/...
   ```

   **SMTP_USERNAME** (for email notifications):

   ```
   <EMAIL>
   ```

   **SMTP_PASSWORD** (for email notifications):

   ```
   your-smtp-password
   ```

### 5.3 Add Repository Variables

Click "Variables" tab, then "New repository variable" for each:

**AZURE_LOCATION**:

```
Chile Central
```

**AZURE_LOCATION_CODE**:

```
 
```

**PROJECT_NAME**:

```
filtro-curricular
```

**TERRAFORM_VERSION**:

```
1.5.0
```

**SMTP_SERVER**:

```
smtp.office365.com
```

**SMTP_PORT**:

```
587
```

### 5.4 Create GitHub Environments

1. **Navigate to Environments**:
   - In your GitHub repository, click "Settings"
   - Click "Environments" in the left sidebar

2. **Create Development Environment**:
   - Click "New environment"
   - **Name**: `dev`
   - No protection rules needed
   - Click "Configure environment"

3. **Create Staging Environment**:
   - Click "New environment"
   - **Name**: `staging`
   - **Protection rules**:
     - Check "Required reviewers"
     - Add 1 reviewer (yourself or team member)
   - Click "Save protection rules"

4. **Create UAT Environment**:
   - Click "New environment"
   - **Name**: `uat`
   - **Protection rules**:
     - Check "Required reviewers"
     - Add 2 reviewers
     - Check "Restrict deployments to specific branches"
     - Add branch: `main`
   - Click "Save protection rules"

5. **Create Production Environment**:
   - Click "New environment"
   - **Name**: `prod`
   - **Protection rules**:
     - Check "Required reviewers"
     - Add 3 reviewers
     - Check "Restrict deployments to specific branches"
     - Add branch: `main`
   - Click "Save protection rules"

### 5.5 Add Environment-Specific Variables

For each environment (dev, staging, uat, prod), add these variables:

**Development Environment Variables**:

- `APP_SERVICE_PLAN_SKU_DEV`: `B1`
- `ACR_SKU_DEV`: `Basic`
- `MIN_INSTANCES_DEV`: `0`
- `MAX_INSTANCES_DEV`: `1`

**Staging Environment Variables**:

- `APP_SERVICE_PLAN_SKU_STAGING`: `S1`
- `ACR_SKU_STAGING`: `Standard`
- `MIN_INSTANCES_STAGING`: `1`
- `MAX_INSTANCES_STAGING`: `2`

**UAT Environment Variables**:

- `APP_SERVICE_PLAN_SKU_UAT`: `S1`
- `ACR_SKU_UAT`: `Standard`
- `MIN_INSTANCES_UAT`: `1`
- `MAX_INSTANCES_UAT`: `2`

**Production Environment Variables**:

- `APP_SERVICE_PLAN_SKU_PROD`: `P1v2`
- `ACR_SKU_PROD`: `Premium`
- `MIN_INSTANCES_PROD`: `2`
- `MAX_INSTANCES_PROD`: `10`

## 🏗️ Step 6: Configure Terraform Backend

### 6.1 Update Terraform Configuration

1. **Create Backend Configuration File**:
   - In your local repository: `apps/infrastructure/azure/filtro-curricular/`
   - Create file: `backend.tf`

```hcl
terraform {
  backend "azurerm" {
    resource_group_name  = "filtro-curricular-terraform-state-rg"
    storage_account_name = "filtrocurriculartfstate"
    container_name       = "terraform-state"
    key                  = "filtro-curricular.tfstate"
  }
}
```

2. **Update Main Terraform File**:
   - Edit `main.tf` to ensure backend configuration is compatible
   - The backend block should be in a separate file for better organization

### 6.2 Initialize Terraform with Remote Backend

1. **Set Environment Variables** (for local testing):

```bash
export ARM_CLIENT_ID="fa81931f-6772-4881-9736-235161dca6af"
export ARM_CLIENT_SECRET="****************************************"
export ARM_SUBSCRIPTION_ID="2ee03089-129c-4624-97c9-990d968e6141"
export ARM_TENANT_ID="bd5360f9-4c71-4f42-8309-9de054b4b337"
```

2. **Initialize Terraform**:

```bash
cd apps/infrastructure/azure/filtro-curricular/
terraform init
```

## ✅ Step 7: Verification Steps

### 7.1 Test Service Principal Permissions

1. **Test Azure Login** (using Azure CLI if available):

```bash
az login --service-principal \
  --username "your-application-client-id" \
  --password "your-client-secret-value" \
  --tenant "your-directory-tenant-id"
```

```bash
az login --service-principal \
  --username "fa81931f-6772-4881-9736-235161dca6af" \
  --password "****************************************" \
  --tenant "bd5360f9-4c71-4f42-8309-9de054b4b337"
```

{
  "clientId": ,
  "clientSecret": "****************************************",
  "subscriptionId": "2ee03089-129c-4624-97c9-990d968e6141",
  "tenantId": "bd5360f9-4c71-4f42-8309-9de054b4b337"
}

2. **Verify Subscription Access**:

```bash
az account show
az account list
```

3. **Test Resource Group Creation**:

```bash
az group create --name "test-sp-permissions" --location "Chile Central"
az group delete --name "test-sp-permissions" --yes
```

### 7.2 Verify GitHub Secrets

1. **Check Secrets Configuration**:
   - Go to GitHub repository → Settings → Secrets and variables → Actions
   - Verify all secrets are listed (values will be hidden)
   - Ensure no typos in secret names

2. **Test Workflow Trigger**:
   - Make a small change to a file in `apps/infrastructure/azure/filtro-curricular/`
   - Commit and push to `dev` branch
   - Check GitHub Actions tab for workflow execution

### 7.3 Verify Terraform State Backend

1. **Check Storage Account Access**:
   - In Azure portal, go to your storage account
   - Click "Containers"
   - Verify `terraform-state` container exists
   - After first Terraform run, verify state file appears

2. **Test Terraform Operations**:

```bash
# Plan should work without errors
terraform plan

# Validate configuration
terraform validate
```

## 🚨 Step 8: Troubleshooting Common Issues

### 8.1 Service Principal Issues

**Problem**: "Insufficient privileges to complete the operation"
**Solution**:

- Verify all three roles are assigned at subscription level
- Check that you have Owner/User Access Administrator role
- Wait 5-10 minutes for role propagation

**Problem**: "Application not found"
**Solution**:

- Verify the clientId in GitHub secrets matches the Application ID
- Ensure the service principal wasn't deleted

### 8.2 Storage Account Issues

**Problem**: "Storage account not found"
**Solution**:

- Verify storage account name is globally unique
- Check that the service principal has "Storage Blob Data Contributor" role
- Ensure the container name matches exactly

### 8.3 GitHub Actions Issues

**Problem**: "Secret not found"
**Solution**:

- Check secret names match exactly (case-sensitive)
- Verify secrets are added at repository level, not organization level
- Ensure environment-specific secrets are in the correct environment

## 📋 Step 9: Final Checklist

Before running your first deployment, verify:

- ✅ Azure subscription ID copied correctly
- ✅ Service Principal created with all required roles
- ✅ Client secret copied immediately after creation
- ✅ Storage account created for Terraform state
- ✅ All GitHub secrets added with correct names
- ✅ All GitHub variables configured
- ✅ GitHub environments created with protection rules
- ✅ Terraform backend configuration updated
- ✅ Service Principal permissions tested
- ✅ Resource providers registered in subscription

## 🎯 Next Steps

1. **First Deployment**:
   - Create a feature branch from `dev`
   - Make a small change to trigger the workflow
   - Create PR and merge to `dev`
   - Monitor GitHub Actions for successful deployment

2. **Monitor Resources**:
   - Check Azure portal for created resources
   - Verify Application Insights is collecting data
   - Test application URLs provided in deployment summary

3. **Production Readiness**:
   - Test staging environment deployment
   - Configure custom domains if needed
   - Set up monitoring alerts
   - Review security configurations

---

**🎉 Congratulations!** Your Azure Service Principal and GitHub Actions CI/CD pipeline are now configured and ready for enterprise-grade deployments!
