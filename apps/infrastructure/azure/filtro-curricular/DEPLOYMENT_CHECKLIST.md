# ✅ Filtro Curricular Azure Deployment - Complete Checklist

This checklist ensures you have completed all necessary steps for a production-ready CI/CD deployment.

## 🎯 Pre-Deployment Checklist

### Azure Portal Configuration

#### Subscription Setup

- [ ] Azure subscription identified and accessible
- [ ] Subscription ID copied and stored securely
- [ ] User has Owner or User Access Administrator role
- [ ] Required resource providers registered:
    - [ ] Microsoft.Web
    - [ ] Microsoft.ContainerRegistry
    - [ ] Microsoft.KeyVault
    - [ ] Microsoft.Storage
    - [ ] Microsoft.Insights

#### Service Principal Creation

- [ ] Service Principal created via Azure Portal
- [ ] Application (client) ID copied
- [ ] Directory (tenant) ID copied
- [ ] Client secret created and copied immediately
- [ ] Service Principal assigned required roles:
    - [ ] Contributor (subscription level)
    - [ ] Key Vault Administrator (subscription level)
    - [ ] User Access Administrator (subscription level)

#### Terraform State Backend

- [ ] Resource group created: `filtro-curricular-terraform-state-rg`
- [ ] Storage account created with globally unique name
- [ ] Container `terraform-state` created in storage account
- [ ] Service Principal assigned "Storage Blob Data Contributor" role on storage account
- [ ] Storage account name documented for backend configuration

### GitHub Repository Configuration

#### Repository Secrets

- [ ] `AZURE_CREDENTIALS` - Complete JSON with clientId, clientSecret, subscriptionId, tenantId
- [ ] `OPENAI_API_KEY` - Your OpenAI API key
- [ ] `OPENAI_TOKEN` - Your OpenAI token
- [ ] `ASSISTANT_ID_JURIDICO` - Legal assistant ID
- [ ] `ASSISTANT_ID_CALIDAD` - Quality assistant ID
- [ ] `NOTIFICATION_EMAIL` - Email for deployment notifications
- [ ] `TEAMS_WEBHOOK_URL` - Microsoft Teams webhook URL
- [ ] `SMTP_USERNAME` - SMTP username for email notifications
- [ ] `SMTP_PASSWORD` - SMTP password for email notifications

#### Repository Variables

- [ ] `AZURE_LOCATION` - Azure region (e.g., "Chile Central")
- [ ] `AZURE_LOCATION_CODE` - Region code (e.g., "eus")
- [ ] `PROJECT_NAME` - "filtro-curricular"
- [ ] `TERRAFORM_VERSION` - "1.5.0"
- [ ] `SMTP_SERVER` - SMTP server address
- [ ] `SMTP_PORT` - SMTP port number

#### GitHub Environments

- [ ] `dev` environment created (no protection rules)
- [ ] `staging` environment created (1 reviewer required)
- [ ] `uat` environment created (2 reviewers, main branch only)
- [ ] `prod` environment created (3 reviewers, main branch only)

#### Environment-Specific Variables

**Development:**

- [ ] `APP_SERVICE_PLAN_SKU_DEV` - "B1"
- [ ] `ACR_SKU_DEV` - "Basic"
- [ ] `MIN_INSTANCES_DEV` - "0"
- [ ] `MAX_INSTANCES_DEV` - "1"

**Staging:**

- [ ] `APP_SERVICE_PLAN_SKU_STAGING` - "S1"
- [ ] `ACR_SKU_STAGING` - "Standard"
- [ ] `MIN_INSTANCES_STAGING` - "1"
- [ ] `MAX_INSTANCES_STAGING` - "2"

**UAT:**

- [ ] `APP_SERVICE_PLAN_SKU_UAT` - "S1"
- [ ] `ACR_SKU_UAT` - "Standard"
- [ ] `MIN_INSTANCES_UAT` - "1"
- [ ] `MAX_INSTANCES_UAT` - "2"

**Production:**

- [ ] `APP_SERVICE_PLAN_SKU_PROD` - "P1v2"
- [ ] `ACR_SKU_PROD` - "Premium"
- [ ] `MIN_INSTANCES_PROD` - "2"
- [ ] `MAX_INSTANCES_PROD` - "10"

### Branch Protection Rules

- [ ] Main branch protection configured:
    - [ ] Require pull request reviews (2 approvals)
    - [ ] Require status checks (terraform-plan, build-and-test)
    - [ ] Require conversation resolution
    - [ ] Include administrators
- [ ] Dev branch protection configured:
    - [ ] Require pull request reviews (1 approval)
    - [ ] Require status checks (terraform-plan, build-and-test)

### Workflow Files

- [ ] `.github/workflows/filtro-curricular-infrastructure.yml` exists
- [ ] `.github/workflows/filtro-curricular-applications.yml` exists
- [ ] Workflow files have correct trigger paths
- [ ] Environment variables properly referenced in workflows

### Terraform Configuration

- [ ] `backend.tf` file created with correct storage account details
- [ ] Backend configuration matches created storage account
- [ ] All Terraform files validated locally (if possible)

## 🧪 Testing and Verification

### Service Principal Testing

#### **Web Method Verification**

- [ ] Service Principal visible in Azure Portal → Azure Active Directory → App registrations
- [ ] All three roles assigned in Subscription → Access control (IAM)
- [ ] Storage account permissions configured

#### **CLI Method Verification**

- [ ] Service Principal authentication tested:

  ```bash
  az login --service-principal \
    --username "your-client-id" \
    --password "your-client-secret" \
    --tenant "your-tenant-id"
  ```

- [ ] Subscription access verified:

  ```bash
  az account show
  az account list
  ```

- [ ] Resource creation permissions confirmed:

  ```bash
  az group create --name "test-permissions" --location "Chile Central"
  az group delete --name "test-permissions" --yes
  ```

- [ ] Role assignments verified:

  ```bash
  az role assignment list --assignee "your-sp-object-id" --output table
  ```

### GitHub Actions Testing

- [ ] Test workflow triggered successfully
- [ ] Infrastructure workflow completes without errors
- [ ] Application workflow completes without errors
- [ ] Terraform state stored correctly in Azure Storage
- [ ] No secrets exposed in workflow logs

### Notification Testing

- [ ] Email notifications received for successful deployment
- [ ] Email notifications received for failed deployment
- [ ] Teams notifications received (if configured)
- [ ] Notification content includes correct information

### Application Testing

- [ ] Backend application accessible via generated URL
- [ ] Frontend application accessible via generated URL
- [ ] Application health checks passing
- [ ] OpenAI integration working correctly
- [ ] File upload functionality working (if applicable)

## 🚀 Deployment Execution

### First Deployment

- [ ] Feature branch created from `dev`
- [ ] Small change made to trigger infrastructure workflow
- [ ] Pull request created and reviewed
- [ ] Pull request merged to `dev` branch
- [ ] Infrastructure deployment completed successfully
- [ ] Azure resources created and accessible

### Application Deployment

- [ ] Application code changes made (if needed)
- [ ] Application workflow triggered
- [ ] Docker images built and pushed to ACR
- [ ] App Services restarted and updated
- [ ] Applications accessible and functional

### Environment Promotion

- [ ] Dev environment tested and validated
- [ ] Changes merged to `main` branch for staging
- [ ] Staging deployment completed successfully
- [ ] Staging environment tested
- [ ] UAT deployment planned (manual trigger)
- [ ] Production deployment planned (manual trigger)

## 🔍 Post-Deployment Verification

### Azure Resources

- [ ] Resource groups created with correct naming convention
- [ ] App Services running and accessible
- [ ] Container Registry contains application images
- [ ] Key Vault contains all required secrets
- [ ] Storage Account accessible and functional
- [ ] Application Insights collecting data
- [ ] Log Analytics workspace receiving logs

### Security Verification

- [ ] No secrets exposed in logs or outputs
- [ ] Managed identities working correctly
- [ ] Key Vault access policies configured properly
- [ ] Network security groups configured (if using VNet)
- [ ] HTTPS enforced on all endpoints

### Monitoring and Logging

- [ ] Application Insights dashboard accessible
- [ ] Application logs visible in Log Analytics
- [ ] Performance metrics being collected
- [ ] Error tracking functional
- [ ] Custom metrics configured (if needed)

### Cost Management

- [ ] Resource tagging applied correctly
- [ ] Cost tracking enabled
- [ ] Auto-shutdown configured for dev environment
- [ ] Resource sizing appropriate for environment
- [ ] Unused resources identified and removed

## 📋 Documentation and Handover

### Documentation Complete

- [ ] All setup guides reviewed and accurate
- [ ] Troubleshooting guides accessible
- [ ] Architecture documentation updated
- [ ] Runbook created for operations team
- [ ] Security documentation completed

### Team Handover

- [ ] Development team trained on deployment process
- [ ] Operations team has access to monitoring tools
- [ ] Incident response procedures documented
- [ ] Escalation contacts identified
- [ ] Knowledge transfer sessions completed

### Maintenance Planning

- [ ] Update schedule planned for dependencies
- [ ] Security patching process defined
- [ ] Backup and recovery procedures tested
- [ ] Disaster recovery plan documented
- [ ] Performance optimization roadmap created

## 🎯 Production Readiness

### Performance

- [ ] Load testing completed (if applicable)
- [ ] Performance benchmarks established
- [ ] Auto-scaling configured and tested
- [ ] Resource limits appropriate
- [ ] CDN configured (if needed)

### Security

- [ ] Security scan completed
- [ ] Vulnerability assessment performed
- [ ] Compliance requirements met
- [ ] Access controls reviewed
- [ ] Audit logging enabled

### Reliability

- [ ] Health checks configured
- [ ] Monitoring alerts set up
- [ ] Backup procedures tested
- [ ] Failover procedures documented
- [ ] SLA requirements defined

## ✅ Final Sign-Off

### Technical Sign-Off

- [ ] Infrastructure team approval
- [ ] Development team approval
- [ ] Security team approval
- [ ] Operations team approval

### Business Sign-Off

- [ ] Product owner approval
- [ ] Stakeholder acceptance
- [ ] Go-live date confirmed
- [ ] Communication plan executed

---

## 🎉 Deployment Complete

Once all items in this checklist are completed, your Filtro Curricular Azure deployment is production-ready with enterprise-grade CI/CD capabilities.

**Next Steps:**

1. Monitor the first few deployments closely
2. Gather feedback from the development team
3. Optimize based on usage patterns
4. Plan for scaling and future enhancements

**Support Resources:**

- [Azure Portal Setup Guide](AZURE_PORTAL_SETUP_GUIDE.md)
- [GitHub Actions Setup Guide](GITHUB_ACTIONS_SETUP.md)
- [Teams Webhook Setup](TEAMS_WEBHOOK_SETUP.md)
- [Troubleshooting Guide](README.md#troubleshooting)
